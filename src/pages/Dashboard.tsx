import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { User } from "@supabase/supabase-js";
import { Button } from "@/components/ui/button";
import { LogOut, Plus, TrendingUp, Users, Target } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CampaignList from "@/components/dashboard/CampaignList";
import MatchingEngine from "@/components/dashboard/MatchingEngine";
import InsightsPanel from "@/components/dashboard/InsightsPanel";

const Dashboard = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<any>(null);
  const [stats, setStats] = useState({
    activeCampaigns: 0,
    totalPublishers: 0,
    matchAccuracy: 0,
  });

  useEffect(() => {
    // Check authentication
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (!session) {
        navigate("/auth");
      } else {
        setUser(session.user);
        loadProfile(session.user.id);
        loadStats();
      }
      setLoading(false);
    });

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (!session) {
        navigate("/auth");
      } else {
        setUser(session.user);
        loadProfile(session.user.id);
        loadStats();
      }
    });

    return () => subscription.unsubscribe();
  }, [navigate]);

  const loadProfile = async (userId: string) => {
    const { data } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();
    
    if (data) {
      setProfile(data);
    }
  };

  const loadStats = async () => {
    // Get active campaigns count
    const { count: activeCampaigns } = await supabase
      .from("campaigns")
      .select("*", { count: "exact", head: true })
      .eq("status", "active");

    // Get total publishers count
    const { count: totalPublishers } = await supabase
      .from("publishers")
      .select("*", { count: "exact", head: true });

    // Get average match accuracy
    const { data: matchData } = await supabase
      .from("matches")
      .select("match_score");

    const matchAccuracy = matchData && matchData.length > 0
      ? Math.round(matchData.reduce((acc, m) => acc + m.match_score, 0) / matchData.length)
      : 0;

    setStats({
      activeCampaigns: activeCampaigns || 0,
      totalPublishers: totalPublishers || 0,
      matchAccuracy,
    });
  };

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    navigate("/");
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse text-primary">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Dashboard</h1>
              <p className="text-sm text-muted-foreground">
                Welcome back, {profile?.full_name || user?.email}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                New Campaign
              </Button>
              <Button variant="ghost" size="sm" onClick={handleSignOut}>
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Stats */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-primary/10">
                <TrendingUp className="w-6 h-6 text-primary" />
              </div>
              <span className="text-sm text-muted-foreground">This Month</span>
            </div>
            <div className="text-3xl font-bold mb-1">{stats.activeCampaigns}</div>
            <div className="text-sm text-muted-foreground">Active Campaigns</div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-accent/10">
                <Users className="w-6 h-6 text-accent" />
              </div>
              <span className="text-sm text-muted-foreground">Available</span>
            </div>
            <div className="text-3xl font-bold mb-1">{stats.totalPublishers}</div>
            <div className="text-sm text-muted-foreground">Publishers</div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-success/10">
                <Target className="w-6 h-6 text-success" />
              </div>
              <span className="text-sm text-muted-foreground">Average</span>
            </div>
            <div className="text-3xl font-bold mb-1">{stats.matchAccuracy}%</div>
            <div className="text-sm text-muted-foreground">Match Accuracy</div>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="matching" className="space-y-6">
          <TabsList>
            <TabsTrigger value="matching">Matching Engine</TabsTrigger>
            <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
            <TabsTrigger value="insights">Market Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="matching">
            <MatchingEngine />
          </TabsContent>

          <TabsContent value="campaigns">
            <CampaignList />
          </TabsContent>

          <TabsContent value="insights">
            <InsightsPanel />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
