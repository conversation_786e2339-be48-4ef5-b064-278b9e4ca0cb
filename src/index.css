@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    --primary: 188 85% 42%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 188 85% 55%;

    --secondary: 215 15% 92%;
    --secondary-foreground: 215 25% 20%;

    --muted: 215 15% 95%;
    --muted-foreground: 215 15% 45%;

    --accent: 14 90% 62%;
    --accent-foreground: 0 0% 100%;
    --accent-glow: 14 95% 72%;

    --success: 142 75% 45%;
    --success-foreground: 0 0% 100%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 20% 88%;
    --input: 215 20% 92%;
    --ring: 188 85% 42%;

    --radius: 0.75rem;

    --gradient-primary: linear-gradient(135deg, hsl(188 85% 42%), hsl(188 85% 55%));
    --gradient-accent: linear-gradient(135deg, hsl(14 90% 62%), hsl(14 95% 72%));
    --gradient-hero: linear-gradient(135deg, hsl(188 85% 42%) 0%, hsl(14 90% 62%) 100%);
    
    --shadow-sm: 0 2px 8px hsla(215, 25%, 15%, 0.06);
    --shadow-md: 0 4px 16px hsla(215, 25%, 15%, 0.08);
    --shadow-lg: 0 8px 32px hsla(215, 25%, 15%, 0.12);
    --shadow-glow: 0 0 40px hsla(188, 85%, 42%, 0.15);

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 215 25% 20%;
    --sidebar-primary: 188 85% 42%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 15% 95%;
    --sidebar-accent-foreground: 215 25% 20%;
    --sidebar-border: 215 20% 90%;
    --sidebar-ring: 188 85% 42%;
  }

  .dark {
    --background: 215 30% 8%;
    --foreground: 210 20% 95%;

    --card: 215 25% 12%;
    --card-foreground: 210 20% 95%;

    --popover: 215 25% 12%;
    --popover-foreground: 210 20% 95%;

    --primary: 188 85% 55%;
    --primary-foreground: 215 30% 8%;
    --primary-glow: 188 85% 65%;

    --secondary: 215 20% 18%;
    --secondary-foreground: 210 20% 95%;

    --muted: 215 20% 16%;
    --muted-foreground: 215 15% 60%;

    --accent: 14 95% 65%;
    --accent-foreground: 215 30% 8%;
    --accent-glow: 14 95% 75%;

    --success: 142 75% 55%;
    --success-foreground: 215 30% 8%;

    --warning: 38 92% 60%;
    --warning-foreground: 215 30% 8%;

    --destructive: 0 75% 60%;
    --destructive-foreground: 210 20% 95%;

    --border: 215 20% 22%;
    --input: 215 20% 18%;
    --ring: 188 85% 55%;

    --gradient-primary: linear-gradient(135deg, hsl(188 85% 45%), hsl(188 85% 60%));
    --gradient-accent: linear-gradient(135deg, hsl(14 90% 58%), hsl(14 95% 70%));
    --gradient-hero: linear-gradient(135deg, hsl(188 85% 45%) 0%, hsl(14 90% 58%) 100%);

    --shadow-sm: 0 2px 8px hsla(0, 0%, 0%, 0.3);
    --shadow-md: 0 4px 16px hsla(0, 0%, 0%, 0.4);
    --shadow-lg: 0 8px 32px hsla(0, 0%, 0%, 0.5);
    --shadow-glow: 0 0 40px hsla(188, 85%, 55%, 0.25);

    --sidebar-background: 215 25% 10%;
    --sidebar-foreground: 210 20% 90%;
    --sidebar-primary: 188 85% 55%;
    --sidebar-primary-foreground: 215 30% 8%;
    --sidebar-accent: 215 20% 16%;
    --sidebar-accent-foreground: 210 20% 90%;
    --sidebar-border: 215 20% 20%;
    --sidebar-ring: 188 85% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
