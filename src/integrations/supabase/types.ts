export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.5"
  }
  public: {
    Tables: {
      campaigns: {
        Row: {
          commission_rate: number
          commission_type: string | null
          cookie_days: number | null
          created_at: string
          description: string | null
          id: string
          merchant_id: string
          name: string
          status: Database["public"]["Enums"]["campaign_status"] | null
          target_epc: number | null
          updated_at: string
        }
        Insert: {
          commission_rate: number
          commission_type?: string | null
          cookie_days?: number | null
          created_at?: string
          description?: string | null
          id?: string
          merchant_id: string
          name: string
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_epc?: number | null
          updated_at?: string
        }
        Update: {
          commission_rate?: number
          commission_type?: string | null
          cookie_days?: number | null
          created_at?: string
          description?: string | null
          id?: string
          merchant_id?: string
          name?: string
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_epc?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "campaigns_merchant_id_fkey"
            columns: ["merchant_id"]
            isOneToOne: false
            referencedRelation: "merchants"
            referencedColumns: ["id"]
          },
        ]
      }
      insights: {
        Row: {
          ai_report: string | null
          bridge_nodes: Json | null
          campaign_id: string | null
          clusters: Json | null
          content_gaps: string[] | null
          created_at: string
          graph_data: Json | null
          id: string
          merchant_id: string | null
        }
        Insert: {
          ai_report?: string | null
          bridge_nodes?: Json | null
          campaign_id?: string | null
          clusters?: Json | null
          content_gaps?: string[] | null
          created_at?: string
          graph_data?: Json | null
          id?: string
          merchant_id?: string | null
        }
        Update: {
          ai_report?: string | null
          bridge_nodes?: Json | null
          campaign_id?: string | null
          clusters?: Json | null
          content_gaps?: string[] | null
          created_at?: string
          graph_data?: Json | null
          id?: string
          merchant_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "insights_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "insights_merchant_id_fkey"
            columns: ["merchant_id"]
            isOneToOne: false
            referencedRelation: "merchants"
            referencedColumns: ["id"]
          },
        ]
      }
      matches: {
        Row: {
          campaign_id: string
          created_at: string
          id: string
          match_reason: string | null
          match_score: number
          performance_score: number
          publisher_id: string
          semantic_score: number
          status: Database["public"]["Enums"]["match_status"] | null
          updated_at: string
        }
        Insert: {
          campaign_id: string
          created_at?: string
          id?: string
          match_reason?: string | null
          match_score: number
          performance_score: number
          publisher_id: string
          semantic_score: number
          status?: Database["public"]["Enums"]["match_status"] | null
          updated_at?: string
        }
        Update: {
          campaign_id?: string
          created_at?: string
          id?: string
          match_reason?: string | null
          match_score?: number
          performance_score?: number
          publisher_id?: string
          semantic_score?: number
          status?: Database["public"]["Enums"]["match_status"] | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "matches_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "matches_publisher_id_fkey"
            columns: ["publisher_id"]
            isOneToOne: false
            referencedRelation: "publishers"
            referencedColumns: ["id"]
          },
        ]
      }
      merchants: {
        Row: {
          created_at: string
          description: string | null
          id: string
          logo_url: string | null
          name: string
          target_epc: number | null
          updated_at: string
          vertical: Database["public"]["Enums"]["vertical_type"]
          website: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          logo_url?: string | null
          name: string
          target_epc?: number | null
          updated_at?: string
          vertical: Database["public"]["Enums"]["vertical_type"]
          website?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          logo_url?: string | null
          name?: string
          target_epc?: number | null
          updated_at?: string
          vertical?: Database["public"]["Enums"]["vertical_type"]
          website?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string
          full_name: string | null
          id: string
          role: Database["public"]["Enums"]["user_role"] | null
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email: string
          full_name?: string | null
          id: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string
          full_name?: string | null
          id?: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string
        }
        Relationships: []
      }
      publishers: {
        Row: {
          avg_epc: number | null
          bio: string | null
          contact_email: string | null
          created_at: string
          id: string
          monthly_traffic: number | null
          name: string
          niche: string[] | null
          updated_at: string
          url: string
        }
        Insert: {
          avg_epc?: number | null
          bio?: string | null
          contact_email?: string | null
          created_at?: string
          id?: string
          monthly_traffic?: number | null
          name: string
          niche?: string[] | null
          updated_at?: string
          url: string
        }
        Update: {
          avg_epc?: number | null
          bio?: string | null
          contact_email?: string | null
          created_at?: string
          id?: string
          monthly_traffic?: number | null
          name?: string
          niche?: string[] | null
          updated_at?: string
          url?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      campaign_status: "draft" | "active" | "paused" | "completed"
      match_status: "pending" | "accepted" | "rejected" | "active" | "completed"
      user_role:
        | "admin"
        | "research_lead"
        | "campaign_manager"
        | "publisher_dev"
      vertical_type:
        | "beauty"
        | "fashion"
        | "electronics"
        | "home"
        | "food"
        | "health"
        | "travel"
        | "education"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      campaign_status: ["draft", "active", "paused", "completed"],
      match_status: ["pending", "accepted", "rejected", "active", "completed"],
      user_role: [
        "admin",
        "research_lead",
        "campaign_manager",
        "publisher_dev",
      ],
      vertical_type: [
        "beauty",
        "fashion",
        "electronics",
        "home",
        "food",
        "health",
        "travel",
        "education",
      ],
    },
  },
} as const
