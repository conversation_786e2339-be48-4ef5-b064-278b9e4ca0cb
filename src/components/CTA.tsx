import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowRight, Mail } from "lucide-react";

const CTA = () => {
  return (
    <section className="py-24">
      <div className="container mx-auto px-4">
        <Card className="relative overflow-hidden bg-gradient-hero p-12 md:p-16 text-center">
          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-primary-foreground/10 rounded-full blur-3xl" />
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-accent-foreground/10 rounded-full blur-3xl" />
          
          <div className="relative z-10 max-w-3xl mx-auto space-y-8">
            <h2 className="text-4xl md:text-5xl font-bold text-primary-foreground">
              Sẵn sàng Transform Affiliate Marketing?
            </h2>
            <p className="text-xl text-primary-foreground/90 leading-relaxed">
              Join early access program và trở thành một trong những affiliate network đầu tiên 
              ứng dụng Graph AI tại Việt Nam
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
              <Button 
                size="lg" 
                className="bg-primary-foreground text-primary hover:bg-primary-foreground/90 gap-2 shadow-lg"
              >
                Đăng ký Early Access
                <ArrowRight className="w-5 h-5" />
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="border-primary-foreground/30 text-primary-foreground hover:bg-primary-foreground/10 gap-2"
              >
                <Mail className="w-5 h-5" />
                Liên hệ Sales
              </Button>
            </div>

            <div className="pt-8 border-t border-primary-foreground/20">
              <p className="text-sm text-primary-foreground/80">
                🚀 MVP Launch: Q1 2026 • 🎯 Target: 50 Merchants, 200 Publishers • 🇻🇳 Made for Vietnam
              </p>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
};

export default CTA;
