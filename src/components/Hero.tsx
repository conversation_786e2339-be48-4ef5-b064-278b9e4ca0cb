import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Sparkles } from "lucide-react";
import heroGraph from "@/assets/hero-graph.jpg";

const Hero = () => {
  return (
    <section className="relative min-h-[90vh] flex items-center justify-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <img 
          src={heroGraph} 
          alt="AI Network Graph" 
          className="w-full h-full object-cover opacity-20"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-background/90" />
      </div>

      {/* Content */}
      <div className="container relative z-10 mx-auto px-4 py-20">
        <div className="max-w-4xl mx-auto text-center space-y-8 animate-fade-in">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary">
            <Sparkles className="w-4 h-4" />
            <span className="text-sm font-medium">Powered by Graph AI + InfraNodus</span>
          </div>

          {/* Main Heading */}
          <h1 className="text-5xl md:text-7xl font-bold leading-tight">
            <span className="bg-gradient-hero bg-clip-text text-transparent">
              AI-Driven Affiliate
            </span>
            <br />
            <span className="text-foreground">Marketing Intelligence</span>
          </h1>

          {/* Subheading */}
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Nền tảng đầu tiên tại Việt Nam sử dụng <span className="text-primary font-semibold">Graph-based AI</span> để 
            phân tích thị trường, ghép đôi merchant ↔ publisher, và tối ưu campaign <span className="text-accent font-semibold">real-time</span>
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
            <Button variant="hero" size="lg" className="gap-2" onClick={() => window.location.href = '/auth'}>
              Bắt đầu ngay
              <ArrowRight className="w-5 h-5" />
            </Button>
            <Button variant="outline" size="lg" onClick={() => window.location.href = '/dashboard'}>
              Xem Demo
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 pt-12 max-w-3xl mx-auto">
            <div className="space-y-2">
              <div className="text-4xl font-bold bg-gradient-primary bg-clip-text text-transparent">85%+</div>
              <div className="text-sm text-muted-foreground">Match Accuracy</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl font-bold bg-gradient-primary bg-clip-text text-transparent">20%+</div>
              <div className="text-sm text-muted-foreground">ROI Lift</div>
            </div>
            <div className="space-y-2">
              <div className="text-4xl font-bold bg-gradient-primary bg-clip-text text-transparent">5 phút</div>
              <div className="text-sm text-muted-foreground">Time to Match</div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background to-transparent z-5" />
    </section>
  );
};

export default Hero;
