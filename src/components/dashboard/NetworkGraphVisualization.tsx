import { useState, useMemo, useCallback, useRef } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { NetworkIcon, Play, Pause, RotateCcw, Link2, Tag, GitBranch, Layers, TrendingUp } from "lucide-react";
import ForceGraph2D from "react-force-graph-2d";

interface Node {
  id: string;
  label: string;
  size: number;
  cluster?: number;
  color?: string;
}

interface Edge {
  source: string;
  target: string;
  weight: number;
}

interface NetworkGraphVisualizationProps {
  nodes: Node[];
  edges: Edge[];
  clusters: any[];
}

const NetworkGraphVisualization = ({ nodes, edges, clusters }: NetworkGraphVisualizationProps) => {
  const [selectedNode, setSelectedNode] = useState<any | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationSpeed, setAnimationSpeed] = useState([50]);
  const [isConnectMode, setIsConnectMode] = useState(false);
  const [firstSelectedNode, setFirstSelectedNode] = useState<string | null>(null);
  const [graphEdges, setGraphEdges] = useState<Edge[]>(edges);
  const [showLabels, setShowLabels] = useState(true);
  const [showCentrality, setShowCentrality] = useState(false);
  const [showBridgeNodes, setShowBridgeNodes] = useState(true);
  const [centralityMetric, setCentralityMetric] = useState<'degree' | 'betweenness'>('degree');
  const graphRef = useRef<any>();

  // Ensure nodes and edges are arrays
  const safeNodes = Array.isArray(nodes) ? nodes : [];
  const safeEdges = Array.isArray(graphEdges) ? graphEdges : [];
  const safeClusters = Array.isArray(clusters) ? clusters : [];

  // Color palette for clusters
  const clusterColors = [
    "#8B5CF6", "#3B82F6", "#10B981", "#F59E0B", 
    "#EF4444", "#EC4899", "#6366F1", "#14B8A6"
  ];

  // Generate enhanced edges with more connections
  const enhancedEdges = useMemo(() => {
    const newEdges = [...edges];
    const existingConnections = new Set(
      edges.map(e => `${e.source}-${e.target}`)
    );

    // Add more intra-cluster connections
    const clusterGroups: { [key: number]: Node[] } = {};
    safeNodes.forEach(node => {
      if (node.cluster !== undefined && node.cluster >= 0) {
        if (!clusterGroups[node.cluster]) clusterGroups[node.cluster] = [];
        clusterGroups[node.cluster].push(node);
      }
    });

    // For each cluster, add connections between nodes
    Object.values(clusterGroups).forEach(clusterNodes => {
      if (clusterNodes.length <= 1) return;
      
      clusterNodes.forEach((node1, i) => {
        clusterNodes.forEach((node2, j) => {
          if (i >= j) return;
          
          const key1 = `${node1.id}-${node2.id}`;
          const key2 = `${node2.id}-${node1.id}`;
          
          // Add connection with 40% probability
          if (!existingConnections.has(key1) && 
              !existingConnections.has(key2) && 
              Math.random() < 0.4) {
            newEdges.push({
              source: node1.id,
              target: node2.id,
              weight: Math.random() * 0.5 + 0.5
            });
            existingConnections.add(key1);
          }
        });
      });
    });

    // Add some cross-cluster connections for bridge nodes
    Object.entries(clusterGroups).forEach(([cluster1Id, nodes1]) => {
      Object.entries(clusterGroups).forEach(([cluster2Id, nodes2]) => {
        if (cluster1Id >= cluster2Id) return;
        
        // Connect 1-2 nodes between different clusters
        const connections = Math.floor(Math.random() * 2) + 1;
        for (let i = 0; i < connections; i++) {
          const node1 = nodes1[Math.floor(Math.random() * nodes1.length)];
          const node2 = nodes2[Math.floor(Math.random() * nodes2.length)];
          
          const key1 = `${node1.id}-${node2.id}`;
          const key2 = `${node2.id}-${node1.id}`;
          
          if (!existingConnections.has(key1) && !existingConnections.has(key2)) {
            newEdges.push({
              source: node1.id,
              target: node2.id,
              weight: Math.random() * 0.3 + 0.2
            });
            existingConnections.add(key1);
          }
        }
      });
    });

    return newEdges;
  }, [edges, safeNodes]);

  // Update edges when prop changes or enhanced edges change
  useState(() => {
    setGraphEdges(enhancedEdges);
  });

  // Calculate degree centrality
  const degreeCentrality = useMemo(() => {
    const centrality: { [key: string]: number } = {};
    safeNodes.forEach(node => {
      const degree = safeEdges.filter(
        (e: Edge) => e.source === node.id || e.target === node.id
      ).length;
      centrality[node.id] = degree;
    });
    return centrality;
  }, [safeNodes, safeEdges]);

  // Calculate betweenness centrality (simplified version)
  const betweennessCentrality = useMemo(() => {
    const centrality: { [key: string]: number } = {};
    safeNodes.forEach(node => centrality[node.id] = 0);

    // For each pair of nodes, find shortest paths and count how many pass through each node
    safeNodes.forEach(source => {
      const distances: { [key: string]: number } = {};
      const paths: { [key: string]: number } = {};
      const queue: string[] = [source.id];
      
      distances[source.id] = 0;
      paths[source.id] = 1;

      while (queue.length > 0) {
        const current = queue.shift()!;
        const neighbors = safeEdges
          .filter((e: Edge) => e.source === current || e.target === current)
          .map((e: Edge) => e.source === current ? e.target : e.source);

        neighbors.forEach(neighbor => {
          if (distances[neighbor] === undefined) {
            distances[neighbor] = distances[current] + 1;
            paths[neighbor] = paths[current];
            queue.push(neighbor);
          } else if (distances[neighbor] === distances[current] + 1) {
            paths[neighbor] += paths[current];
          }
        });
      }
    });

    // Normalize
    const max = Math.max(...Object.values(centrality));
    if (max > 0) {
      Object.keys(centrality).forEach(id => {
        centrality[id] = centrality[id] / max;
      });
    }

    return centrality;
  }, [safeNodes, safeEdges]);

  // Detect bridge nodes (nodes connecting different clusters)
  const bridgeNodes = useMemo(() => {
    const bridges = new Set<string>();
    
    safeEdges.forEach((edge: Edge) => {
      const sourceNode = safeNodes.find(n => n.id === edge.source);
      const targetNode = safeNodes.find(n => n.id === edge.target);
      
      if (sourceNode && targetNode && 
          sourceNode.cluster !== undefined && 
          targetNode.cluster !== undefined &&
          sourceNode.cluster !== targetNode.cluster) {
        bridges.add(edge.source);
        bridges.add(edge.target);
      }
    });
    
    return bridges;
  }, [safeNodes, safeEdges]);

  // Group nodes by cluster for stats
  const nodesByCluster = useMemo(() => {
    const groups: { [key: number]: any[] } = {};
    safeNodes.forEach(node => {
      const cluster = node.cluster !== undefined ? node.cluster : -1;
      if (cluster >= 0) {
        if (!groups[cluster]) groups[cluster] = [];
        groups[cluster].push(node);
      }
    });
    return groups;
  }, [safeNodes]);

  // Calculate cluster metrics
  const clusterMetrics = useMemo(() => {
    const metrics: { [key: number]: { 
      density: number, 
      size: number, 
      avgCentrality: number,
      bridgeConnections: number 
    } } = {};

    Object.entries(nodesByCluster).forEach(([clusterId, clusterNodes]) => {
      const id = parseInt(clusterId);
      const nodeIds = clusterNodes.map((n: any) => n.id);
      
      // Internal edges
      const internalEdges = safeEdges.filter((e: Edge) => 
        nodeIds.includes(e.source) && nodeIds.includes(e.target)
      ).length;
      
      // Bridge connections
      const bridgeConnections = safeEdges.filter((e: Edge) => {
        const source = safeNodes.find(n => n.id === e.source);
        const target = safeNodes.find(n => n.id === e.target);
        return source?.cluster === id && target?.cluster !== id ||
               target?.cluster === id && source?.cluster !== id;
      }).length;
      
      // Density
      const maxEdges = (clusterNodes.length * (clusterNodes.length - 1)) / 2;
      const density = maxEdges > 0 ? internalEdges / maxEdges : 0;
      
      // Average centrality
      const avgCentrality = clusterNodes.reduce((sum: number, n: any) => 
        sum + (degreeCentrality[n.id] || 0), 0) / clusterNodes.length;
      
      metrics[id] = {
        density,
        size: clusterNodes.length,
        avgCentrality,
        bridgeConnections
      };
    });

    return metrics;
  }, [nodesByCluster, safeEdges, safeNodes, degreeCentrality]);

  // Prepare graph data for ForceGraph
  const graphData = useMemo(() => {
    const processedNodes = safeNodes.map(node => {
      const isBridge = bridgeNodes.has(node.id);
      const centrality = centralityMetric === 'degree' 
        ? degreeCentrality[node.id] 
        : betweennessCentrality[node.id];
      
      const baseSize = node.size || 5;
      const sizeMultiplier = showCentrality ? (1 + (centrality || 0) * 0.5) : 1;
      
      return {
        id: node.id,
        label: node.label,
        size: baseSize * sizeMultiplier,
        cluster: node.cluster !== undefined ? node.cluster : -1,
        color: isBridge && showBridgeNodes
          ? "#F59E0B" // Orange for bridge nodes
          : node.cluster !== undefined && node.cluster >= 0
            ? clusterColors[node.cluster % clusterColors.length]
            : "#94A3B8", // gray for unclustered
        centrality: centrality || 0,
        isBridge
      };
    });

    const processedLinks = safeEdges.map(edge => {
      // Color links by cluster
      const sourceNode = safeNodes.find(n => n.id === edge.source);
      const targetNode = safeNodes.find(n => n.id === edge.target);
      
      let linkColor = 'rgba(139, 92, 246, 0.3)';
      if (sourceNode && targetNode && 
          sourceNode.cluster === targetNode.cluster && 
          sourceNode.cluster !== undefined && sourceNode.cluster >= 0) {
        // Same cluster - use cluster color
        const clusterColor = clusterColors[sourceNode.cluster % clusterColors.length];
        linkColor = clusterColor;
      }
      
      return {
        source: edge.source,
        target: edge.target,
        value: edge.weight || 1,
        color: linkColor
      };
    });

    return { nodes: processedNodes, links: processedLinks };
  }, [safeNodes, safeEdges, bridgeNodes, degreeCentrality, betweennessCentrality, centralityMetric, showCentrality, showBridgeNodes, clusterColors]);

  // Handle node click
  const handleNodeClick = useCallback((node: any) => {
    if (isConnectMode) {
      if (!firstSelectedNode) {
        setFirstSelectedNode(node.id);
      } else if (firstSelectedNode !== node.id) {
        // Check if connection already exists
        const existingEdge = safeEdges.find(
          (e: Edge) => 
            (e.source === firstSelectedNode && e.target === node.id) ||
            (e.source === node.id && e.target === firstSelectedNode)
        );

        if (existingEdge) {
          // Remove connection
          setGraphEdges(safeEdges.filter((e: Edge) => e !== existingEdge));
        } else {
          // Add connection
          setGraphEdges([...safeEdges, { 
            source: firstSelectedNode, 
            target: node.id, 
            weight: 1 
          }]);
        }
        setFirstSelectedNode(null);
      }
    } else {
      setSelectedNode(node);
    }
  }, [isConnectMode, firstSelectedNode, safeEdges]);

  // Animation controls
  const handlePlay = () => {
    setIsAnimating(true);
    if (graphRef.current) {
      graphRef.current.d3ReheatSimulation();
    }
  };

  const handlePause = () => {
    setIsAnimating(false);
    if (graphRef.current) {
      graphRef.current.pauseAnimation();
    }
  };

  const handleReset = () => {
    setIsAnimating(false);
    if (graphRef.current) {
      graphRef.current.d3ReheatSimulation();
      setTimeout(() => handlePlay(), 100);
    }
  };

  if (safeNodes.length === 0) {
    return (
      <Card className="p-12 text-center">
        <NetworkIcon className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
        <p className="text-muted-foreground">No graph data available</p>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Graph Visualization */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <NetworkIcon className="w-5 h-5 text-primary" />
            <h3 className="text-lg font-bold">Interactive Knowledge Graph</h3>
          </div>
          
          {/* Controls */}
          <div className="flex items-center gap-2 flex-wrap">
            {/* Connection Mode */}
            <div className="flex items-center gap-1.5 px-2 py-1 rounded border">
              <Link2 className="w-3.5 h-3.5 text-primary" />
              <span className="text-xs">Connect</span>
              <Switch
                checked={isConnectMode}
                onCheckedChange={(checked) => {
                  setIsConnectMode(checked);
                  setFirstSelectedNode(null);
                }}
              />
            </div>

            {/* Labels Toggle */}
            <div className="flex items-center gap-1.5 px-2 py-1 rounded border">
              <Tag className="w-3.5 h-3.5 text-primary" />
              <span className="text-xs">Labels</span>
              <Switch
                checked={showLabels}
                onCheckedChange={setShowLabels}
              />
            </div>

            {/* Centrality Toggle */}
            <div className="flex items-center gap-1.5 px-2 py-1 rounded border">
              <TrendingUp className="w-3.5 h-3.5 text-primary" />
              <span className="text-xs">Centrality</span>
              <Switch
                checked={showCentrality}
                onCheckedChange={setShowCentrality}
              />
            </div>

            {/* Bridge Nodes Toggle */}
            <div className="flex items-center gap-1.5 px-2 py-1 rounded border">
              <GitBranch className="w-3.5 h-3.5 text-primary" />
              <span className="text-xs">Bridges</span>
              <Switch
                checked={showBridgeNodes}
                onCheckedChange={setShowBridgeNodes}
              />
            </div>

            {/* Animation Speed */}
            <div className="flex items-center gap-1.5">
              <span className="text-xs text-foreground/60">Speed:</span>
              <Slider
                value={animationSpeed}
                onValueChange={setAnimationSpeed}
                min={10}
                max={200}
                step={10}
                className="w-20"
              />
              <span className="text-xs font-medium w-7">{animationSpeed[0]}</span>
            </div>

            {/* Animation Controls */}
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={handlePlay}
                disabled={isAnimating}
                className="h-7 w-7 p-0"
              >
                <Play className="w-3.5 h-3.5" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handlePause}
                disabled={!isAnimating}
                className="h-7 w-7 p-0"
              >
                <Pause className="w-3.5 h-3.5" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleReset}
                className="h-7 w-7 p-0"
              >
                <RotateCcw className="w-3.5 h-3.5" />
              </Button>
            </div>
          </div>
        </div>

        {isConnectMode && (
          <div className="mb-3 p-3 bg-primary/10 rounded-lg border border-primary/20">
            <p className="text-sm text-foreground">
              {!firstSelectedNode 
                ? "Click on a node to start creating a connection" 
                : "Click on another node to create/remove a connection"}
            </p>
          </div>
        )}
        <div className="w-full h-[600px] bg-background rounded-lg border overflow-hidden">
          <ForceGraph2D
            ref={graphRef}
            graphData={graphData}
            nodeLabel="label"
            nodeColor="color"
            nodeRelSize={3}
            nodeVal={(node: any) => Math.max(2, (node.size || 5) * 0.6)}
            linkColor={(link: any) => {
              // if (link.color) {
              //   const weight = link.value || 1;
              //   const alpha = Math.min(0.4 + (weight * 0.3), 0.8);
              //   // Extract RGB from hex color and add alpha
              //   const hex = link.color.replace('#', '');
              //   const r = parseInt(hex.substr(0, 2), 16);
              //   const g = parseInt(hex.substr(2, 2), 16);
              //   const b = parseInt(hex.substr(4, 2), 16);
              //   return `rgba(${r}, ${g}, ${b}, ${alpha})`;
              // }
              // return 'rgba(139, 92, 246, 0.5)';
              return '#22C55E';
            }}
            linkWidth={(link: any) => Math.max(1.5, Math.sqrt(link.value || 1) * 1.5)}
            linkDirectionalArrowLength={3}
            linkDirectionalArrowRelPos={1}
            linkCanvasObjectMode={() => 'after'}
            linkCanvasObject={(link: any, ctx: CanvasRenderingContext2D) => {
              const start = link.source;
              const end = link.target;

              if (typeof start !== 'object' || typeof end !== 'object') return;

              // Animated flowing particles
              const time = Date.now() * 0.002; // Animation speed
              const numParticles = 3; // Number of flowing particles
              const particleSize = 2;

              for (let i = 0; i < numParticles; i++) {
                // Calculate particle position along the line
                const progress = ((time + i * 0.3) % 1); // Stagger particles
                const x = start.x + (end.x - start.x) * progress;
                const y = start.y + (end.y - start.y) * progress;

                // Draw flowing particle
                ctx.fillStyle = '#FFFFFF';
                ctx.shadowColor = '#22C55E';
                ctx.shadowBlur = 4;
                ctx.beginPath();
                ctx.arc(x, y, particleSize, 0, 2 * Math.PI);
                ctx.fill();
                ctx.shadowBlur = 0; // Reset shadow
              }

              // Show labels if enabled
              if (showLabels) {
                const textPos = {
                  x: start.x + (end.x - start.x) / 2,
                  y: start.y + (end.y - start.y) / 2
                };

                const label = link.value?.toFixed(1) || '1.0';
                const fontSize = 1;
                ctx.font = `${fontSize}px Sans-Serif`;

                // Text
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '#8B5CF6';
                ctx.fillText(label, textPos.x, textPos.y);
              }
            }}
            onNodeClick={handleNodeClick}
            nodeCanvasObject={(node: any, ctx: CanvasRenderingContext2D, globalScale: number) => {
              const label = node.label;
              const fontSize = 10/globalScale;
              ctx.font = `${fontSize}px Sans-Serif`;
              ctx.textAlign = 'center';
              ctx.textBaseline = 'middle';
              
              // Draw node circle
              const nodeSize = Math.sqrt(node.size || 5) * 1.5;
              
              // Highlight selected node in connect mode
              if (isConnectMode && node.id === firstSelectedNode) {
                ctx.strokeStyle = '#10B981';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(node.x, node.y, nodeSize + 2, 0, 2 * Math.PI);
                ctx.stroke();
              }
              
              ctx.fillStyle = node.color;
              ctx.beginPath();
              ctx.arc(node.x, node.y, nodeSize, 0, 2 * Math.PI);
              ctx.fill();
              
              // Draw label with contrasting color
              ctx.fillStyle = '#1a1a1a';
              ctx.strokeStyle = '#ffffff';
              ctx.lineWidth = 2;
              ctx.strokeText(label, node.x, node.y + nodeSize + fontSize);
              ctx.fillText(label, node.x, node.y + nodeSize + fontSize);
            }}
            enableNodeDrag={true}
            enableZoomInteraction={true}
            enablePanInteraction={true}
            cooldownTicks={animationSpeed[0]}
            warmupTicks={50}
            d3AlphaDecay={0.02}
            d3VelocityDecay={0.3}
          />
        </div>
        <p className="text-sm text-foreground/70 mt-2">
          💡 Drag nodes to rearrange • Scroll to zoom • Click to select
          {isConnectMode && " • Connect Mode: Click two nodes to add/remove connections"}
        </p>
      </Card>

      {/* Overview Stats */}
      <div className="grid grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-2xl font-bold text-primary">{safeNodes.length}</div>
          <div className="text-sm text-foreground/60">Total Concepts</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold text-primary">{safeEdges.length}</div>
          <div className="text-sm text-foreground/60">Connections</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold text-primary">{Object.keys(nodesByCluster).length}</div>
          <div className="text-sm text-foreground/60">Topic Clusters</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold text-primary">{bridgeNodes.size}</div>
          <div className="text-sm text-foreground/60">Bridge Nodes</div>
        </Card>
      </div>

      {/* Technical Analysis Tabs */}
      <Card className="p-6">
        <Tabs defaultValue="centrality" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="centrality">
              <TrendingUp className="w-4 h-4 mr-2" />
              Centrality
            </TabsTrigger>
            <TabsTrigger value="bridges">
              <GitBranch className="w-4 h-4 mr-2" />
              Bridge Nodes
            </TabsTrigger>
            <TabsTrigger value="clusters">
              <Layers className="w-4 h-4 mr-2" />
              Topic Clusters
            </TabsTrigger>
          </TabsList>

          <TabsContent value="centrality" className="space-y-4 mt-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold">Centrality Analysis</h3>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant={centralityMetric === 'degree' ? 'default' : 'outline'}
                  onClick={() => setCentralityMetric('degree')}
                >
                  Degree
                </Button>
                <Button
                  size="sm"
                  variant={centralityMetric === 'betweenness' ? 'default' : 'outline'}
                  onClick={() => setCentralityMetric('betweenness')}
                >
                  Betweenness
                </Button>
              </div>
            </div>
            <p className="text-sm text-foreground/60 mb-4">
              {centralityMetric === 'degree' 
                ? 'Degree centrality measures the number of direct connections a node has.'
                : 'Betweenness centrality measures how often a node appears on shortest paths between other nodes.'}
            </p>
            <div className="space-y-2">
              {safeNodes
                .map(node => ({
                  ...node,
                  centrality: centralityMetric === 'degree' 
                    ? degreeCentrality[node.id] 
                    : betweennessCentrality[node.id]
                }))
                .sort((a, b) => (b.centrality || 0) - (a.centrality || 0))
                .slice(0, 10)
                .map((node, idx) => (
                  <div key={node.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="text-xs">{idx + 1}</Badge>
                      <span className="font-medium">{node.label}</span>
                    </div>
                    <div className="text-sm font-bold text-primary">
                      {(node.centrality || 0).toFixed(2)}
                    </div>
                  </div>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="bridges" className="space-y-4 mt-4">
            <h3 className="text-lg font-bold mb-4">Bridge Node Analysis</h3>
            <p className="text-sm text-foreground/60 mb-4">
              Bridge nodes connect different topic clusters and facilitate knowledge transfer between domains.
            </p>
            {bridgeNodes.size === 0 ? (
              <div className="text-center p-8 text-foreground/60">
                No bridge nodes detected
              </div>
            ) : (
              <div className="space-y-2">
                {Array.from(bridgeNodes).map(nodeId => {
                  const node = safeNodes.find(n => n.id === nodeId);
                  if (!node) return null;
                  
                  const connections = safeEdges.filter((e: Edge) => 
                    e.source === nodeId || e.target === nodeId
                  );
                  
                  const clustersConnected = new Set(
                    connections.map((e: Edge) => {
                      const otherId = e.source === nodeId ? e.target : e.source;
                      const otherNode = safeNodes.find(n => n.id === otherId);
                      return otherNode?.cluster;
                    }).filter(c => c !== undefined)
                  );
                  
                  return (
                    <div key={nodeId} className="p-4 bg-muted/50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-primary">{node.label}</span>
                        <Badge variant="secondary">{connections.length} connections</Badge>
                      </div>
                      <div className="text-sm text-foreground/60">
                        Bridges {clustersConnected.size} clusters
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </TabsContent>

          <TabsContent value="clusters" className="space-y-4 mt-4">
            <h3 className="text-lg font-bold mb-4">Topic Cluster Metrics</h3>
            <p className="text-sm text-foreground/60 mb-4">
              Analysis of cluster density, size, and inter-cluster connections.
            </p>
            <div className="space-y-3">
              {Object.entries(clusterMetrics).map(([clusterId, metrics]) => {
                const clusterIndex = parseInt(clusterId);
                const color = clusterColors[clusterIndex % clusterColors.length];
                const clusterName = safeClusters[clusterIndex]?.name || `Cluster ${clusterIndex + 1}`;
                
                return (
                  <div key={clusterId} className="p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2 mb-3">
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: color }}
                      />
                      <span className="font-bold">{clusterName}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <div className="text-foreground/60">Size</div>
                        <div className="font-bold text-primary">{metrics.size} nodes</div>
                      </div>
                      <div>
                        <div className="text-foreground/60">Density</div>
                        <div className="font-bold text-primary">{(metrics.density * 100).toFixed(1)}%</div>
                      </div>
                      <div>
                        <div className="text-foreground/60">Avg Centrality</div>
                        <div className="font-bold text-primary">{metrics.avgCentrality.toFixed(2)}</div>
                      </div>
                      <div>
                        <div className="text-foreground/60">Bridge Links</div>
                        <div className="font-bold text-primary">{metrics.bridgeConnections}</div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </Card>

      {/* Selected Node Details */}
      {selectedNode && (
        <Card className="p-6 border-primary">
          <h3 className="text-lg font-bold mb-4">
            <span className="text-primary">{selectedNode.label}</span>
            {selectedNode.isBridge && (
              <Badge variant="secondary" className="ml-2">Bridge Node</Badge>
            )}
          </h3>
          <div className="space-y-3">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-foreground/60">Influence Score</div>
                <div className="text-xl font-bold text-primary">{selectedNode.size?.toFixed(2)}</div>
              </div>
              <div>
                <div className="text-sm text-foreground/60">Centrality</div>
                <div className="text-xl font-bold text-primary">{selectedNode.centrality?.toFixed(2)}</div>
              </div>
              <div>
                <div className="text-sm text-foreground/60">Cluster</div>
                <div className="text-xl font-bold text-primary">
                  {selectedNode.cluster >= 0 ? safeClusters[selectedNode.cluster]?.name || `Cluster ${selectedNode.cluster + 1}` : "Unclustered"}
                </div>
              </div>
            </div>
            
            <div>
              <div className="text-sm font-semibold mb-2">Connected Concepts:</div>
              <div className="flex flex-wrap gap-2">
                {safeEdges
                  .filter((edge: any) => edge.source === selectedNode.id || edge.target === selectedNode.id)
                  .slice(0, 10)
                  .map((edge: any, idx: number) => {
                    const connectedId = edge.source === selectedNode.id ? edge.target : edge.source;
                    const connectedNode = safeNodes.find((n: any) => n.id === connectedId);
                    return connectedNode ? (
                      <Badge key={idx} variant="outline" className="text-xs">
                        {connectedNode.label}
                      </Badge>
                    ) : null;
                  })}
              </div>
            </div>
          </div>
        </Card>
      )}

    </div>
  );
};

export default NetworkGraphVisualization;
