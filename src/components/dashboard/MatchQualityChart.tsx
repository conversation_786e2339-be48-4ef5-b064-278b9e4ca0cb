import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from "recharts";
import { TrendingUp, Target, Award } from "lucide-react";

interface MatchStats {
  scoreRange: string;
  count: number;
  percentage: number;
  color: string;
}

const MatchQualityChart = () => {
  const [stats, setStats] = useState<MatchStats[]>([]);
  const [totalMatches, setTotalMatches] = useState(0);
  const [avgScore, setAvgScore] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMatchStats();
  }, []);

  const loadMatchStats = async () => {
    const { data: matches, error } = await supabase
      .from("matches")
      .select("match_score");

    if (error) {
      console.error("Error loading match stats:", error);
      setLoading(false);
      return;
    }

    if (!matches || matches.length === 0) {
      setLoading(false);
      return;
    }

    // Calculate statistics
    const total = matches.length;
    const average = Math.round(
      matches.reduce((sum, m) => sum + m.match_score, 0) / total
    );

    // Group by score ranges
    const ranges = {
      "90-100": { count: 0, color: "hsl(var(--success))" },
      "80-89": { count: 0, color: "hsl(var(--primary))" },
      "70-79": { count: 0, color: "hsl(var(--accent))" },
      "60-69": { count: 0, color: "hsl(var(--warning))" },
      "50-59": { count: 0, color: "hsl(var(--destructive))" },
    };

    matches.forEach((match) => {
      const score = match.match_score;
      if (score >= 90) ranges["90-100"].count++;
      else if (score >= 80) ranges["80-89"].count++;
      else if (score >= 70) ranges["70-79"].count++;
      else if (score >= 60) ranges["60-69"].count++;
      else if (score >= 50) ranges["50-59"].count++;
    });

    const chartData: MatchStats[] = Object.entries(ranges).map(([range, data]) => ({
      scoreRange: range,
      count: data.count,
      percentage: Math.round((data.count / total) * 100),
      color: data.color,
    }));

    setStats(chartData);
    setTotalMatches(total);
    setAvgScore(average);
    setLoading(false);
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse text-muted-foreground text-center">
          Loading match quality data...
        </div>
      </Card>
    );
  }

  if (totalMatches === 0) {
    return (
      <Card className="p-6">
        <div className="text-center text-muted-foreground">
          No match data available yet. Generate matches to see quality distribution.
        </div>
      </Card>
    );
  }

  const getQualityLabel = (range: string) => {
    switch (range) {
      case "90-100":
        return "Excellent";
      case "80-89":
        return "Good";
      case "70-79":
        return "Fair";
      case "60-69":
        return "Low";
      case "50-59":
        return "Poor";
      default:
        return range;
    }
  };

  return (
    <Card className="p-6">
      <div className="mb-6">
        <h3 className="text-xl font-bold mb-1">Match Quality Distribution</h3>
        <p className="text-sm text-muted-foreground">
          Confidence score breakdown across all campaigns
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="p-4 rounded-lg bg-primary/5 border border-primary/20">
          <div className="flex items-center gap-2 mb-2">
            <Target className="w-4 h-4 text-primary" />
            <span className="text-xs text-muted-foreground">Total Matches</span>
          </div>
          <div className="text-2xl font-bold text-primary">{totalMatches}</div>
        </div>

        <div className="p-4 rounded-lg bg-accent/5 border border-accent/20">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-4 h-4 text-accent" />
            <span className="text-xs text-muted-foreground">Avg Score</span>
          </div>
          <div className="text-2xl font-bold text-accent">{avgScore}%</div>
        </div>

        <div className="p-4 rounded-lg bg-success/5 border border-success/20">
          <div className="flex items-center gap-2 mb-2">
            <Award className="w-4 h-4 text-success" />
            <span className="text-xs text-muted-foreground">High Quality</span>
          </div>
          <div className="text-2xl font-bold text-success">
            {stats.find((s) => s.scoreRange === "90-100")?.count || 0}
          </div>
        </div>
      </div>

      {/* Bar Chart */}
      <div className="mb-6">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={stats}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="scoreRange"
              tick={{ fill: "hsl(var(--muted-foreground))", fontSize: 12 }}
            />
            <YAxis
              tick={{ fill: "hsl(var(--muted-foreground))", fontSize: 12 }}
              label={{
                value: "Number of Matches",
                angle: -90,
                position: "insideLeft",
                style: { fill: "hsl(var(--muted-foreground))", fontSize: 12 },
              }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "hsl(var(--background))",
                border: "1px solid hsl(var(--border))",
                borderRadius: "8px",
              }}
              formatter={(value: number, name: string, props: any) => [
                `${value} matches (${props.payload.percentage}%)`,
                getQualityLabel(props.payload.scoreRange),
              ]}
            />
            <Bar dataKey="count" radius={[8, 8, 0, 0]}>
              {stats.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Score Range Legend */}
      <div className="space-y-2">
        <h4 className="text-sm font-semibold mb-3">Quality Breakdown</h4>
        {stats.map((stat) => (
          <div
            key={stat.scoreRange}
            className="flex items-center justify-between p-3 rounded-lg bg-muted/30"
          >
            <div className="flex items-center gap-3">
              <div
                className="w-4 h-4 rounded"
                style={{ backgroundColor: stat.color }}
              />
              <div>
                <span className="font-medium">{stat.scoreRange}</span>
                <span className="text-sm text-muted-foreground ml-2">
                  {getQualityLabel(stat.scoreRange)}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Badge variant="secondary">{stat.count} matches</Badge>
              <span className="text-sm font-semibold">{stat.percentage}%</span>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default MatchQualityChart;
