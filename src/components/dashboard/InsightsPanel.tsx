import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { NetworkIcon, Brain, TrendingUp, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import MatchQualityChart from "./MatchQualityChart";
import GraphAnalysisModal from "./GraphAnalysisModal";

interface Insight {
  id: string;
  ai_report: string;
  content_gaps: string[];
  clusters: any;
  bridge_nodes: any;
  graph_data: any;
  created_at: string;
}

const InsightsPanel = () => {
  const { toast } = useToast();
  const [insights, setInsights] = useState<Insight[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedInsight, setSelectedInsight] = useState<Insight | null>(null);
  const [modalOpen, setModalOpen] = useState(false);

  useEffect(() => {
    loadInsights();
  }, []);

  const loadInsights = async () => {
    const { data, error } = await supabase
      .from("insights")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(10);

    if (error) {
      toast({
        variant: "destructive",
        title: "Error loading insights",
        description: error.message,
      });
    } else {
      setInsights(data || []);
    }
    setLoading(false);
  };

  const handleViewAnalysis = (insight: Insight) => {
    setSelectedInsight(insight);
    setModalOpen(true);
  };

  if (loading) {
    return (
      <Card className="p-12 text-center">
        <div className="animate-pulse text-muted-foreground">Loading insights...</div>
      </Card>
    );
  }

  if (insights.length === 0) {
    return (
      <Card className="p-12 text-center">
        <div className="mb-4">
          <NetworkIcon className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-xl font-bold mb-2">No Market Insights Yet</h3>
          <p className="text-muted-foreground mb-6">
            Run InfraNodus graph analysis to discover market trends, content gaps, and strategic opportunities
          </p>
        </div>
        <Button variant="gradient">
          <Brain className="w-4 h-4 mr-2" />
          Generate First Insight
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold mb-1">Market Intelligence</h3>
          <p className="text-sm text-muted-foreground">
            AI-powered insights from InfraNodus graph analysis
          </p>
        </div>
        <Button variant="gradient">
          <Brain className="w-4 h-4 mr-2" />
          Generate New Insight
        </Button>
      </div>

      {/* Match Quality Distribution Chart */}
      <MatchQualityChart />

      {insights.map((insight) => (
        <Card key={insight.id} className="p-6">
          <div className="flex items-start gap-4 mb-4">
            <div className="p-3 rounded-xl bg-primary/10">
              <Brain className="w-6 h-6 text-primary" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-lg font-bold">AI Analysis Report</h4>
                <span className="text-xs text-muted-foreground">
                  {new Date(insight.created_at).toLocaleDateString("vi-VN")}
                </span>
              </div>
              {insight.ai_report && (
                <p className="text-sm text-muted-foreground leading-relaxed mb-4">
                  {insight.ai_report}
                </p>
              )}
            </div>
          </div>

          {/* Content Gaps */}
          {insight.content_gaps && insight.content_gaps.length > 0 && (
            <div className="mb-4 p-4 rounded-lg bg-accent/5 border border-accent/20">
              <div className="flex items-center gap-2 mb-3">
                <AlertCircle className="w-5 h-5 text-accent" />
                <h5 className="font-semibold text-accent">Content Gaps Discovered</h5>
              </div>
              <div className="flex flex-wrap gap-2">
                {insight.content_gaps.map((gap, idx) => (
                  <Badge key={idx} variant="secondary" className="text-sm">
                    {gap}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Graph Stats */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="p-4 rounded-lg bg-muted/50">
              <div className="flex items-center gap-2 mb-1">
                <TrendingUp className="w-4 h-4 text-primary" />
                <span className="text-xs text-muted-foreground">Clusters</span>
              </div>
              <div className="text-2xl font-bold">
                {insight.clusters && Array.isArray(insight.clusters) ? insight.clusters.length : 0}
              </div>
            </div>

            <div className="p-4 rounded-lg bg-muted/50">
              <div className="flex items-center gap-2 mb-1">
                <NetworkIcon className="w-4 h-4 text-accent" />
                <span className="text-xs text-muted-foreground">Bridge Nodes</span>
              </div>
              <div className="text-2xl font-bold">
                {insight.bridge_nodes && Array.isArray(insight.bridge_nodes) ? insight.bridge_nodes.length : 0}
              </div>
            </div>

            <div className="p-4 rounded-lg bg-muted/50 col-span-2 md:col-span-1">
              <div className="flex items-center gap-2 mb-1">
                <AlertCircle className="w-4 h-4 text-success" />
                <span className="text-xs text-muted-foreground">Opportunities</span>
              </div>
              <div className="text-2xl font-bold">
                {insight.content_gaps ? insight.content_gaps.length : 0}
              </div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t">
            <Button variant="outline" size="sm" onClick={() => handleViewAnalysis(insight)}>
              View Full Graph Analysis
            </Button>
          </div>
        </Card>
      ))}

      {/* Graph Analysis Modal */}
      {selectedInsight && (
        <GraphAnalysisModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          insight={selectedInsight}
        />
      )}
    </div>
  );
};

export default InsightsPanel;
