import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Play, Pause, BarChart } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Campaign {
  id: string;
  name: string;
  description: string;
  commission_rate: number;
  commission_type: string;
  status: string;
  target_epc: number;
  created_at: string;
}

const CampaignList = () => {
  const { toast } = useToast();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCampaigns();
  }, []);

  const loadCampaigns = async () => {
    const { data, error } = await supabase
      .from("campaigns")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      toast({
        variant: "destructive",
        title: "Error loading campaigns",
        description: error.message,
      });
    } else {
      setCampaigns(data || []);
    }
    setLoading(false);
  };

  const toggleCampaignStatus = async (id: string, currentStatus: string) => {
    const newStatus = currentStatus === "active" ? "paused" : "active";
    
    const { error } = await supabase
      .from("campaigns")
      .update({ status: newStatus })
      .eq("id", id);

    if (error) {
      toast({
        variant: "destructive",
        title: "Error updating campaign",
        description: error.message,
      });
    } else {
      toast({
        title: "Campaign updated",
        description: `Campaign is now ${newStatus}`,
      });
      loadCampaigns();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-success text-success-foreground";
      case "paused":
        return "bg-accent text-accent-foreground";
      case "draft":
        return "bg-muted text-muted-foreground";
      default:
        return "bg-secondary text-secondary-foreground";
    }
  };

  if (loading) {
    return (
      <Card className="p-12 text-center">
        <div className="animate-pulse text-muted-foreground">Loading campaigns...</div>
      </Card>
    );
  }

  if (campaigns.length === 0) {
    return (
      <Card className="p-12 text-center">
        <div className="mb-4 text-muted-foreground">No campaigns yet</div>
        <Button variant="gradient">Create Your First Campaign</Button>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold">All Campaigns ({campaigns.length})</h3>
        <Button variant="gradient">
          Create New Campaign
        </Button>
      </div>

      {campaigns.map((campaign) => (
        <Card key={campaign.id} className="p-6 hover:shadow-md transition-all">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h4 className="text-lg font-bold">{campaign.name}</h4>
                <Badge className={getStatusColor(campaign.status)}>
                  {campaign.status}
                </Badge>
              </div>
              
              {campaign.description && (
                <p className="text-sm text-muted-foreground mb-4">
                  {campaign.description}
                </p>
              )}
              
              <div className="flex gap-6 text-sm">
                <div>
                  <span className="text-muted-foreground">Commission:</span>{" "}
                  <span className="font-semibold">
                    {campaign.commission_rate}% {campaign.commission_type}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">Target EPC:</span>{" "}
                  <span className="font-semibold">
                    {campaign.target_epc.toLocaleString()}đ
                  </span>
                </div>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleCampaignStatus(campaign.id, campaign.status)}
              >
                {campaign.status === "active" ? (
                  <>
                    <Pause className="w-4 h-4 mr-1" />
                    Pause
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-1" />
                    Activate
                  </>
                )}
              </Button>
              <Button variant="ghost" size="sm">
                <BarChart className="w-4 h-4 mr-1" />
                Analytics
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default CampaignList;
