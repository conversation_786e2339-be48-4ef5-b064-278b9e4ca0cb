import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { NetworkIcon, TrendingUp, Link2, AlertCircle, BarChart3 } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import NetworkGraphVisualization from "./NetworkGraphVisualization";

interface Cluster {
  id: string;
  name: string;
  size: number;
  keywords: string[];
  centrality?: number;
}

interface BridgeNode {
  keyword: string;
  connections: number;
  betweenness: number;
  clusters: string[];
}

interface GraphData {
  nodes: Array<{ id: string; label: string; size: number }>;
  edges: Array<{ source: string; target: string; weight: number }>;
  metrics?: {
    density?: number;
    modularity?: number;
    avgDegree?: number;
  };
}

interface GraphAnalysisModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  insight: {
    id: string;
    ai_report: string;
    content_gaps: string[];
    clusters: Cluster[] | any;
    bridge_nodes: BridgeNode[] | any;
    graph_data: GraphData | any;
    created_at: string;
  };
}

const GraphAnalysisModal = ({ open, onOpenChange, insight }: GraphAnalysisModalProps) => {
  const clusters = Array.isArray(insight.clusters) ? insight.clusters : [];
  const bridgeNodes = Array.isArray(insight.bridge_nodes) ? insight.bridge_nodes : [];
  const graphData = insight.graph_data || {};

  // Calculate network metrics
  const totalNodes = graphData.nodes?.length || 0;
  const totalEdges = graphData.edges?.length || 0;
  const networkDensity = graphData.metrics?.density || 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <NetworkIcon className="w-5 h-5 text-primary" />
            Full Graph Analysis Report
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="h-[70vh] pr-4">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="graph">Graph View</TabsTrigger>
              <TabsTrigger value="clusters">Clusters</TabsTrigger>
              <TabsTrigger value="bridges">Bridge Nodes</TabsTrigger>
              <TabsTrigger value="network">Network Stats</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4 mt-6">
              <Card className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <BarChart3 className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-bold">AI Analysis Summary</h3>
                </div>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {insight.ai_report || "No AI report available"}
                </p>
              </Card>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card className="p-4">
                  <div className="text-2xl font-bold text-primary">{clusters.length}</div>
                  <div className="text-xs text-muted-foreground">Topic Clusters</div>
                </Card>
                <Card className="p-4">
                  <div className="text-2xl font-bold text-accent">{bridgeNodes.length}</div>
                  <div className="text-xs text-muted-foreground">Bridge Keywords</div>
                </Card>
                <Card className="p-4">
                  <div className="text-2xl font-bold text-success">{totalNodes}</div>
                  <div className="text-xs text-muted-foreground">Network Nodes</div>
                </Card>
                <Card className="p-4">
                  <div className="text-2xl font-bold text-warning">{insight.content_gaps?.length || 0}</div>
                  <div className="text-xs text-muted-foreground">Content Gaps</div>
                </Card>
              </div>

              {/* Content Gaps */}
              {insight.content_gaps && insight.content_gaps.length > 0 && (
                <Card className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <AlertCircle className="w-5 h-5 text-accent" />
                    <h3 className="text-lg font-bold">Content Opportunities</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    {insight.content_gaps.map((gap, idx) => (
                      <div key={idx} className="p-3 rounded-lg bg-accent/5 border border-accent/20">
                        <span className="text-sm font-medium">{gap}</span>
                      </div>
                    ))}
                  </div>
                </Card>
              )}
            </TabsContent>

            {/* Graph Visualization Tab */}
            <TabsContent value="graph" className="mt-6">
              <NetworkGraphVisualization
                nodes={(() => {
                  // Generate nodes from clusters and bridge nodes
                  const generatedNodes: any[] = [];
                  
                  // Add cluster keywords as nodes
                  clusters.forEach((cluster: any, clusterIdx: number) => {
                    if (cluster.keywords && Array.isArray(cluster.keywords)) {
                      cluster.keywords.forEach((keyword: string, idx: number) => {
                        generatedNodes.push({
                          id: `${clusterIdx}-${idx}`,
                          label: keyword,
                          size: cluster.centrality ? cluster.centrality * 10 : 5,
                          cluster: clusterIdx,
                        });
                      });
                    } else if (cluster.name) {
                      // If no keywords, use cluster name
                      generatedNodes.push({
                        id: `cluster-${clusterIdx}`,
                        label: cluster.name,
                        size: (cluster.size || 10) / 2,
                        cluster: clusterIdx,
                      });
                    }
                  });
                  
                  // Add bridge nodes
                  bridgeNodes.forEach((bridge: any, idx: number) => {
                    if (bridge.keyword || bridge.term) {
                      generatedNodes.push({
                        id: `bridge-${idx}`,
                        label: bridge.keyword || bridge.term,
                        size: (bridge.betweenness || 0.5) * 15,
                        cluster: -1, // Special cluster for bridge nodes
                      });
                    }
                  });
                  
                  return generatedNodes;
                })()}
                edges={(() => {
                  // Generate edges connecting nodes within same cluster and bridge nodes
                  const generatedEdges: any[] = [];
                  let edgeId = 0;
                  
                  // Connect nodes within same cluster
                  const nodesByCluster: { [key: number]: any[] } = {};
                  clusters.forEach((cluster: any, clusterIdx: number) => {
                    if (cluster.keywords && Array.isArray(cluster.keywords)) {
                      nodesByCluster[clusterIdx] = cluster.keywords.map((k: string, idx: number) => `${clusterIdx}-${idx}`);
                    }
                  });
                  
                  // Create edges within clusters
                  Object.values(nodesByCluster).forEach((clusterNodes) => {
                    for (let i = 0; i < clusterNodes.length - 1; i++) {
                      for (let j = i + 1; j < Math.min(clusterNodes.length, i + 3); j++) {
                        generatedEdges.push({
                          source: clusterNodes[i],
                          target: clusterNodes[j],
                          weight: Math.random() * 0.5 + 0.5,
                        });
                      }
                    }
                  });
                  
                  // Connect bridge nodes to cluster nodes
                  bridgeNodes.forEach((bridge: any, bridgeIdx: number) => {
                    const bridgeId = `bridge-${bridgeIdx}`;
                    const targetClusters = bridge.clusters || [0, 1];
                    
                    targetClusters.forEach((clusterIdx: number) => {
                      if (nodesByCluster[clusterIdx] && nodesByCluster[clusterIdx].length > 0) {
                        const targetNode = nodesByCluster[clusterIdx][0];
                        generatedEdges.push({
                          source: bridgeId,
                          target: targetNode,
                          weight: bridge.betweenness || 0.8,
                        });
                      }
                    });
                  });
                  
                  return generatedEdges;
                })()}
                clusters={clusters}
              />
            </TabsContent>

            {/* Clusters Tab */}
            <TabsContent value="clusters" className="space-y-4 mt-6">
              <div className="mb-4">
                <h3 className="text-lg font-bold mb-2">Topic Clusters</h3>
                <p className="text-sm text-muted-foreground">
                  Semantic groups of related concepts discovered in the content network
                </p>
              </div>

              {clusters.length === 0 ? (
                <Card className="p-12 text-center">
                  <p className="text-muted-foreground">No clusters data available</p>
                </Card>
              ) : (
                <div className="space-y-4">
                  {clusters.map((cluster: Cluster, idx: number) => (
                    <Card key={idx} className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="text-lg font-bold">
                              {cluster.name || `Cluster ${idx + 1}`}
                            </h4>
                            <Badge variant="secondary">
                              {cluster.size || cluster.keywords?.length || 0} keywords
                            </Badge>
                          </div>
                          {cluster.centrality && (
                            <div className="text-sm text-muted-foreground mb-3">
                              Centrality Score: <span className="font-semibold">{cluster.centrality.toFixed(2)}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {cluster.keywords && cluster.keywords.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {cluster.keywords.map((keyword: string, kidx: number) => (
                            <Badge key={kidx} variant="outline" className="text-xs">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            {/* Bridge Nodes Tab */}
            <TabsContent value="bridges" className="space-y-4 mt-6">
              <div className="mb-4">
                <h3 className="text-lg font-bold mb-2">Bridge Keywords</h3>
                <p className="text-sm text-muted-foreground">
                  Strategic keywords that connect different topic clusters
                </p>
              </div>

              {bridgeNodes.length === 0 ? (
                <Card className="p-12 text-center">
                  <p className="text-muted-foreground">No bridge nodes data available</p>
                </Card>
              ) : (
                <div className="space-y-3">
                  {bridgeNodes.map((node: BridgeNode, idx: number) => (
                    <Card key={idx} className="p-5">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 flex-1">
                          <div className="p-2 rounded-lg bg-primary/10">
                            <Link2 className="w-5 h-5 text-primary" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-bold text-base mb-1">
                              {node.keyword || `Node ${idx + 1}`}
                            </h4>
                            {node.clusters && node.clusters.length > 0 && (
                              <div className="flex flex-wrap gap-1">
                                {node.clusters.map((cluster: string, cidx: number) => (
                                  <Badge key={cidx} variant="secondary" className="text-xs">
                                    {cluster}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          {node.connections && (
                            <div className="text-sm font-semibold text-primary">
                              {node.connections} connections
                            </div>
                          )}
                          {node.betweenness && (
                            <div className="text-xs text-muted-foreground">
                              Betweenness: {node.betweenness.toFixed(3)}
                            </div>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            {/* Network Stats Tab */}
            <TabsContent value="network" className="space-y-4 mt-6">
              <div className="mb-4">
                <h3 className="text-lg font-bold mb-2">Network Topology</h3>
                <p className="text-sm text-muted-foreground">
                  Statistical analysis of the content network structure
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Card className="p-6">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-3 rounded-xl bg-primary/10">
                      <NetworkIcon className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">Total Nodes</div>
                      <div className="text-2xl font-bold">{totalNodes}</div>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Unique concepts in the network
                  </p>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-3 rounded-xl bg-accent/10">
                      <Link2 className="w-6 h-6 text-accent" />
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">Total Edges</div>
                      <div className="text-2xl font-bold">{totalEdges}</div>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Connections between concepts
                  </p>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-3 rounded-xl bg-success/10">
                      <TrendingUp className="w-6 h-6 text-success" />
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">Network Density</div>
                      <div className="text-2xl font-bold">{(networkDensity * 100).toFixed(1)}%</div>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    How interconnected the network is
                  </p>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-3 rounded-xl bg-warning/10">
                      <BarChart3 className="w-6 h-6 text-warning" />
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground">Avg Degree</div>
                      <div className="text-2xl font-bold">
                        {graphData.metrics?.avgDegree?.toFixed(1) || 'N/A'}
                      </div>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Average connections per node
                  </p>
                </Card>
              </div>

              {graphData.metrics?.modularity && (
                <Card className="p-6">
                  <div className="mb-3">
                    <div className="text-sm font-semibold mb-1">Modularity Score</div>
                    <div className="text-3xl font-bold text-primary">
                      {graphData.metrics.modularity.toFixed(3)}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Measures how well the network divides into clusters. Higher values (0.3+) indicate strong community structure.
                  </p>
                </Card>
              )}

              {/* Top Nodes by Degree */}
              {graphData.nodes && graphData.nodes.length > 0 && (
                <Card className="p-6">
                  <h4 className="font-bold mb-4">Top Keywords by Influence</h4>
                  <div className="space-y-3">
                    {graphData.nodes
                      .sort((a: any, b: any) => (b.size || 0) - (a.size || 0))
                      .slice(0, 10)
                      .map((node: any, idx: number) => (
                        <div key={idx} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                          <span className="font-medium">{node.label || node.id}</span>
                          <Badge variant="secondary">
                            Score: {node.size?.toFixed(2) || 0}
                          </Badge>
                        </div>
                      ))}
                  </div>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default GraphAnalysisModal;
