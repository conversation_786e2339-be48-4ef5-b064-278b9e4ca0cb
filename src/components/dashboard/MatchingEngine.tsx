import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Sparkles, Send, TrendingUp, ExternalLink } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Progress } from "@/components/ui/progress";

interface Campaign {
  id: string;
  name: string;
  description: string;
  merchant_id: string;
}

interface Match {
  id: string;
  publisher_id: string;
  match_score: number;
  semantic_score: number;
  performance_score: number;
  match_reason: string;
  publisher: {
    name: string;
    url: string;
    niche: string[];
    monthly_traffic: number;
    avg_epc: number;
  };
}

const MatchingEngine = () => {
  const { toast } = useToast();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<string>("");
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);

  useEffect(() => {
    loadCampaigns();
  }, []);

  useEffect(() => {
    if (selectedCampaign) {
      loadMatches(selectedCampaign);
    }
  }, [selectedCampaign]);

  const loadCampaigns = async () => {
    const { data, error } = await supabase
      .from("campaigns")
      .select("*")
      .eq("status", "active")
      .order("created_at", { ascending: false });

    if (error) {
      toast({
        variant: "destructive",
        title: "Error loading campaigns",
        description: error.message,
      });
    } else {
      setCampaigns(data || []);
      if (data && data.length > 0) {
        setSelectedCampaign(data[0].id);
      }
    }
  };

  const loadMatches = async (campaignId: string) => {
    setLoading(true);
    const { data, error } = await supabase
      .from("matches")
      .select(`
        *,
        publisher:publishers(*)
      `)
      .eq("campaign_id", campaignId)
      .order("match_score", { ascending: false });

    if (error) {
      toast({
        variant: "destructive",
        title: "Error loading matches",
        description: error.message,
      });
    } else {
      setMatches(data as any || []);
    }
    setLoading(false);
  };

  const generateMatches = async () => {
    if (!selectedCampaign) return;

    setGenerating(true);
    toast({
      title: "Generating matches...",
      description: "AI is analyzing publishers and calculating match scores",
    });

    try {
      const { data, error } = await supabase.functions.invoke('generate-matches', {
        body: { campaignId: selectedCampaign }
      });

      if (error) throw error;

      toast({
        title: "Matches generated!",
        description: data.message || "AI-powered matches generated successfully!",
      });
      
      loadMatches(selectedCampaign);
    } catch (error) {
      console.error('Error generating matches:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate matches",
      });
    } finally {
      setGenerating(false);
    }
  };

  const sendOffer = async (matchId: string) => {
    const { error } = await supabase
      .from("matches")
      .update({ status: "active" })
      .eq("id", matchId);

    if (error) {
      toast({
        variant: "destructive",
        title: "Error sending offer",
        description: error.message,
      });
    } else {
      toast({
        title: "Offer sent!",
        description: "Publisher will receive the campaign offer",
      });
      loadMatches(selectedCampaign);
    }
  };

  return (
    <div className="space-y-6">
      {/* Campaign Selection */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-xl font-bold mb-1">Select Campaign</h3>
            <p className="text-sm text-muted-foreground">
              Choose a campaign to find matching publishers
            </p>
          </div>
          <Button
            variant="gradient"
            onClick={generateMatches}
            disabled={!selectedCampaign || generating}
          >
            <Sparkles className="w-4 h-4 mr-2" />
            {generating ? "Generating..." : "Generate Matches"}
          </Button>
        </div>

        <Select value={selectedCampaign} onValueChange={setSelectedCampaign}>
          <SelectTrigger>
            <SelectValue placeholder="Select a campaign" />
          </SelectTrigger>
          <SelectContent>
            {campaigns.map((campaign) => (
              <SelectItem key={campaign.id} value={campaign.id}>
                {campaign.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </Card>

      {/* Match Results */}
      {loading ? (
        <Card className="p-12 text-center">
          <div className="animate-pulse text-muted-foreground">Loading matches...</div>
        </Card>
      ) : matches.length === 0 ? (
        <Card className="p-12 text-center">
          <div className="mb-4 text-muted-foreground">
            No matches found for this campaign yet
          </div>
          <Button variant="gradient" onClick={generateMatches} disabled={generating}>
            <Sparkles className="w-4 h-4 mr-2" />
            Generate Matches with AI
          </Button>
        </Card>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              Top Matches ({matches.length})
            </h3>
            <Badge variant="secondary">
              Average Score: {Math.round(matches.reduce((acc, m) => acc + m.match_score, 0) / matches.length)}
            </Badge>
          </div>

          {matches.map((match) => (
            <Card key={match.id} className="p-6 hover:shadow-md transition-all">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h4 className="text-xl font-bold">{match.publisher.name}</h4>
                    <a
                      href={match.publisher.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary/80"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {match.publisher.niche.map((n, i) => (
                      <Badge key={i} variant="secondary">{n}</Badge>
                    ))}
                  </div>
                  <div className="flex gap-4 text-sm text-muted-foreground">
                    <span>{match.publisher.monthly_traffic.toLocaleString()} visitors/month</span>
                    <span>•</span>
                    <span>EPC: {match.publisher.avg_epc.toLocaleString()}đ</span>
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-4xl font-bold text-primary mb-1">
                    {match.match_score}
                  </div>
                  <div className="text-sm text-muted-foreground mb-2">Match Score</div>
                  <Button size="sm" variant="accent" onClick={() => sendOffer(match.id)}>
                    <Send className="w-4 h-4 mr-1" />
                    Send Offer
                  </Button>
                </div>
              </div>

              {/* Score Breakdown */}
              <div className="space-y-3 mb-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-muted-foreground">Semantic Score</span>
                    <span className="font-semibold">{match.semantic_score}%</span>
                  </div>
                  <Progress value={match.semantic_score} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-muted-foreground">Performance Score</span>
                    <span className="font-semibold">{match.performance_score}%</span>
                  </div>
                  <Progress value={match.performance_score} className="h-2" />
                </div>
              </div>

              {/* Match Reason */}
              <div className="p-4 rounded-lg bg-primary/5 border border-primary/10">
                <div className="flex items-start gap-2">
                  <TrendingUp className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-semibold text-sm mb-1 text-primary">AI Analysis</div>
                    <p className="text-sm text-muted-foreground">{match.match_reason}</p>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default MatchingEngine;
