import { But<PERSON> } from "@/components/ui/button";
import { NetworkIcon, Menu } from "lucide-react";

const Header = () => {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center gap-2">
            <div className="w-10 h-10 rounded-xl bg-gradient-primary flex items-center justify-center">
              <NetworkIcon className="w-6 h-6 text-primary-foreground" />
            </div>
            <div>
              <div className="font-bold text-lg leading-tight">AffiniGraph AI</div>
              <div className="text-xs text-muted-foreground">by InfraNodus</div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-6">
            <a href="#features" className="text-sm font-medium hover:text-primary transition-colors">
              Features
            </a>
            <a href="#layers" className="text-sm font-medium hover:text-primary transition-colors">
              3 Layers
            </a>
            <a href="#dashboard" className="text-sm font-medium hover:text-primary transition-colors">
              Dashboard
            </a>
            <a href="#pricing" className="text-sm font-medium hover:text-primary transition-colors">
              Pricing
            </a>
          </nav>

          {/* CTA Buttons */}
          <div className="flex items-center gap-3">
            <Button variant="ghost" className="hidden sm:inline-flex" onClick={() => window.location.href = '/auth'}>
              Đăng nhập
            </Button>
            <Button variant="gradient" onClick={() => window.location.href = '/auth'}>
              Dùng thử
            </Button>
            <Button variant="ghost" size="icon" className="md:hidden">
              <Menu className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
