import { Card } from "@/components/ui/card";
import { TrendingUp, Users, Zap, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";

const layers = [
  {
    number: "01",
    title: "Product Strategy & Intelligence",
    description: "<PERSON><PERSON> tích thị trường bằng InfraNodus graph, tìm content gaps, và tạo insights chiến lư<PERSON><PERSON> từ reviews & merchant data",
    icon: TrendingUp,
    color: "primary",
    features: [
      "Import Amazon/Shopee data",
      "Graph analysis: centrality, clusters, bridge nodes",
      "AI-generated market insights"
    ]
  },
  {
    number: "02",
    title: "Scoring & Matching Engine",
    description: "Tự động ghép đôi merchant với publisher phù hợp nhất dựa trên Semantic Match Score và performance data",
    icon: Users,
    color: "accent",
    features: [
      "Semantic Match Score (0-100)",
      "Auto-suggest top publishers",
      "Match reasoning với bridge nodes"
    ]
  },
  {
    number: "03",
    title: "Affiliate Campaign Optimization",
    description: "Tối ưu campaign real-time với AI content suggestions và auto-bid adjustment để maximize ROI",
    icon: Zap,
    color: "success",
    features: [
      "Real-time EPC tracking",
      "AI Content Suggestion (RAG)",
      "Auto-bid optimization"
    ]
  }
];

const LayerOverview = () => {
  return (
    <section className="py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16 space-y-4">
          <h2 className="text-4xl md:text-5xl font-bold">
            3 Layers Hoạt động
          </h2>
          <p className="text-xl text-muted-foreground">
            Hệ thống AI đa tầng từ insight đến execution
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {layers.map((layer, index) => {
            const Icon = layer.icon;
            return (
              <Card 
                key={index}
                className="relative p-8 hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/50 group"
              >
                {/* Layer Number */}
                <div className="absolute -top-4 -left-4 w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center text-primary-foreground font-bold text-xl shadow-glow">
                  {layer.number}
                </div>

                {/* Icon */}
                <div className="mb-6 pt-4">
                  <div className="w-14 h-14 rounded-xl bg-primary/10 flex items-center justify-center group-hover:scale-110 transition-transform">
                    <Icon className="w-7 h-7 text-primary" />
                  </div>
                </div>

                {/* Title & Description */}
                <h3 className="text-2xl font-bold mb-4 group-hover:text-primary transition-colors">
                  {layer.title}
                </h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  {layer.description}
                </p>

                {/* Features */}
                <ul className="space-y-3 mb-6">
                  {layer.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start gap-2 text-sm">
                      <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                      <span className="text-foreground">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* CTA */}
                <Button variant="ghost" className="w-full group/btn">
                  Tìm hiểu thêm
                  <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                </Button>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default LayerOverview;
