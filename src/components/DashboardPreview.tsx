import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { NetworkIcon, TrendingUp, Target, Sparkles } from "lucide-react";

const DashboardPreview = () => {
  return (
    <section className="py-24">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16 space-y-4">
          <h2 className="text-4xl md:text-5xl font-bold">
            Dashboard Thông Minh
          </h2>
          <p className="text-xl text-muted-foreground">
            Quản lý toàn bộ affiliate operations từ một nơi
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Graph Analysis Card */}
          <Card className="p-8 bg-gradient-to-br from-card to-primary/5 border-2 hover:border-primary/30 transition-all">
            <div className="flex items-start justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold mb-2">Graph Analysis</h3>
                <p className="text-muted-foreground">InfraNodus powered insights</p>
              </div>
              <div className="p-3 rounded-xl bg-primary/10">
                <NetworkIcon className="w-6 h-6 text-primary" />
              </div>
            </div>

            {/* Mock Graph Visualization */}
            <div className="aspect-video bg-gradient-to-br from-primary/10 to-accent/10 rounded-xl border-2 border-dashed border-primary/20 flex items-center justify-center mb-6">
              <div className="text-center space-y-2">
                <div className="flex justify-center gap-2 mb-4">
                  <div className="w-12 h-12 rounded-full bg-primary/20 animate-pulse" />
                  <div className="w-12 h-12 rounded-full bg-accent/20 animate-pulse delay-100" />
                  <div className="w-12 h-12 rounded-full bg-primary/20 animate-pulse delay-200" />
                </div>
                <p className="text-sm text-muted-foreground">Network Graph Visualization</p>
                <Badge variant="secondary">5 Clusters • 32 Nodes</Badge>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Content Gap</span>
                <span className="font-semibold text-accent">"Cruelty-free skincare"</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Bridge Nodes</span>
                <Badge>8 discovered</Badge>
              </div>
            </div>
          </Card>

          {/* Match Score Card */}
          <Card className="p-8 bg-gradient-to-br from-card to-accent/5 border-2 hover:border-accent/30 transition-all">
            <div className="flex items-start justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold mb-2">Publisher Matching</h3>
                <p className="text-muted-foreground">AI-powered recommendations</p>
              </div>
              <div className="p-3 rounded-xl bg-accent/10">
                <Target className="w-6 h-6 text-accent" />
              </div>
            </div>

            {/* Match List */}
            <div className="space-y-4 mb-6">
              {[
                { name: "BeautyVN Blog", score: 94, niche: "Skincare", traffic: "50K" },
                { name: "Review365.vn", score: 89, niche: "Beauty", traffic: "120K" },
                { name: "Làm Đẹp 24h", score: 87, niche: "Cosmetics", traffic: "80K" },
              ].map((publisher, idx) => (
                <div key={idx} className="flex items-center justify-between p-4 rounded-lg bg-background border hover:border-accent/50 transition-all">
                  <div className="flex-1">
                    <div className="font-semibold mb-1">{publisher.name}</div>
                    <div className="flex gap-2 text-xs text-muted-foreground">
                      <span>{publisher.niche}</span>
                      <span>•</span>
                      <span>{publisher.traffic}/month</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <div className="text-2xl font-bold text-accent">{publisher.score}</div>
                      <div className="text-xs text-muted-foreground">Match Score</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <Button variant="accent" className="w-full">
              Send Offers to Top 3
            </Button>
          </Card>
        </div>

        {/* Optimization Card - Full Width */}
        <Card className="p-8 bg-gradient-to-r from-card via-success/5 to-card border-2 hover:border-success/30 transition-all">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6">
            <div className="flex items-start gap-4">
              <div className="p-3 rounded-xl bg-success/10">
                <Sparkles className="w-6 h-6 text-success" />
              </div>
              <div>
                <h3 className="text-2xl font-bold mb-2">Campaign Optimization</h3>
                <p className="text-muted-foreground">Real-time AI suggestions to boost performance</p>
              </div>
            </div>
            <div className="flex items-center gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-success">+35%</div>
                <div className="text-sm text-muted-foreground">EPC Increase</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">120K</div>
                <div className="text-sm text-muted-foreground">Current EPC</div>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 rounded-lg bg-success/10 border border-success/20">
            <div className="flex items-start gap-3">
              <TrendingUp className="w-5 h-5 text-success mt-0.5" />
              <div>
                <div className="font-semibold mb-2 text-success">AI Recommendation</div>
                <p className="text-sm text-muted-foreground mb-3">
                  Publisher "BeautyVN Blog" content đã drift từ "acne" sang "makeup". 
                  Tăng bid +15% để maintain visibility.
                </p>
                <div className="flex gap-2">
                  <Badge variant="secondary">Auto-applied</Badge>
                  <Badge variant="outline">+15% bid adjustment</Badge>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
};

export default DashboardPreview;
