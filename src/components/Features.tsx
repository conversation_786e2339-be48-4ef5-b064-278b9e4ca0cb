import { Card } from "@/components/ui/card";
import { 
  <PERSON>, 
  Zap, 
  Target, 
  TrendingUp, 
  Clock, 
  Shield 
} from "lucide-react";

const features = [
  {
    icon: Brain,
    title: "InfraNodus Graph AI",
    description: "Phân tích thị trường sâu với graph-based AI, phát hiện trends và content gaps không thể thấy bằng mắt thường"
  },
  {
    icon: Target,
    title: "Smart Matching",
    description: "Semantic Match Score 85%+ accuracy, tự động tìm publisher phù hợp nhất cho mỗi campaign"
  },
  {
    icon: Zap,
    title: "Real-time Optimization",
    description: "AI tối ưu bid và content suggestions theo thời gian thực, tăng ROI ngay lập tức"
  },
  {
    icon: TrendingUp,
    title: "ROI Lift 20%+",
    description: "Dựa trên pilot với 50 merchants, hệ thống đã giúp tăng ROI trung bình 20-35%"
  },
  {
    icon: Clock,
    title: "5 Phút Setup",
    description: "Từ import data đến launch campaign chỉ mất 5 phút, thay vì 5-7 ngày như trước"
  },
  {
    icon: Shield,
    title: "Vietnam-First",
    description: "<PERSON><PERSON><PERSON>c thiết kế cho thị trường Việt Nam, hỗ trợ tiếng Việt và tích hợp Shopee, Tiki"
  }
];

const Features = () => {
  return (
    <section className="py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16 space-y-4">
          <h2 className="text-4xl md:text-5xl font-bold">
            Tại sao chọn AffiniGraph AI?
          </h2>
          <p className="text-xl text-muted-foreground">
            Công nghệ AI tiên tiến kết hợp với hiểu biết sâu về thị trường Việt Nam
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card 
                key={index}
                className="p-6 hover:shadow-md transition-all duration-300 border hover:border-primary/30 group"
              >
                <div className="mb-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                    <Icon className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-3 group-hover:text-primary transition-colors">
                  {feature.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Features;
