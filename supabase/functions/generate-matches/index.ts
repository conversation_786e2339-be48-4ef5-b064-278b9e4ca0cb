import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { campaignId } = await req.json();
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get campaign details
    const { data: campaign, error: campaignError } = await supabaseClient
      .from('campaigns')
      .select('*, merchants(*)')
      .eq('id', campaignId)
      .single();

    if (campaignError) throw campaignError;

    // Get all publishers
    const { data: publishers, error: publishersError } = await supabaseClient
      .from('publishers')
      .select('*');

    if (publishersError) throw publishersError;

    console.log(`Generating matches for campaign: ${campaign.name}`);

    // Calculate matches using AI
    const matches = [];
    const LOVABLE_API_KEY = Deno.env.get('LOVABLE_API_KEY');

    for (const publisher of publishers) {
      try {
        const prompt = `Analyze the semantic similarity between this campaign and publisher:

Campaign: ${campaign.name}
Description: ${campaign.description || 'No description'}
Merchant: ${campaign.merchants?.name || 'Unknown'}
Vertical: ${campaign.merchants?.vertical || 'Unknown'}
Commission: ${campaign.commission_rate}% ${campaign.commission_type}

Publisher: ${publisher.name}
URL: ${publisher.url}
Niches: ${publisher.niche?.join(', ') || 'No niches specified'}
Bio: ${publisher.bio || 'No bio'}
Monthly Traffic: ${publisher.monthly_traffic || 0}
Average EPC: $${(publisher.avg_epc || 0) / 100}

Evaluate on a scale of 0-100:
1. Semantic Score (0-100): How well does the publisher's niche and content align with the campaign's vertical and goals?
2. Performance Score (0-100): Based on traffic and EPC, how likely is success?
3. Provide a brief reason (max 100 words) why this is or isn't a good match.

Respond in JSON format:
{
  "semantic_score": <number>,
  "performance_score": <number>,
  "reason": "<string>"
}`;

        const response = await fetch('https://ai.gateway.lovable.dev/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${LOVABLE_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'google/gemini-2.5-flash',
            messages: [
              { 
                role: 'system', 
                content: 'You are an expert at analyzing campaign-publisher compatibility. Always respond with valid JSON only.' 
              },
              { role: 'user', content: prompt }
            ],
            temperature: 0.7,
          }),
        });

        if (!response.ok) {
          console.error(`AI API error for publisher ${publisher.name}:`, response.status);
          continue;
        }

        const aiData = await response.json();
        const content = aiData.choices[0].message.content;
        
        // Parse JSON from response
        let analysis;
        try {
          const jsonMatch = content.match(/\{[\s\S]*\}/);
          analysis = jsonMatch ? JSON.parse(jsonMatch[0]) : null;
        } catch (parseError) {
          console.error(`Failed to parse AI response for ${publisher.name}:`, parseError);
          continue;
        }

        if (!analysis) continue;

        const semanticScore = Math.min(100, Math.max(0, analysis.semantic_score || 0));
        const performanceScore = Math.min(100, Math.max(0, analysis.performance_score || 0));
        const matchScore = Math.round((semanticScore + performanceScore) / 2);

        // Only create matches for scores above 50
        if (matchScore >= 50) {
          matches.push({
            campaign_id: campaignId,
            publisher_id: publisher.id,
            match_score: matchScore,
            semantic_score: semanticScore,
            performance_score: performanceScore,
            match_reason: analysis.reason || 'AI analysis completed',
            status: 'pending',
          });
        }

        console.log(`Match for ${publisher.name}: ${matchScore}%`);
      } catch (error) {
        console.error(`Error processing publisher ${publisher.name}:`, error);
        continue;
      }
    }

    // Delete existing matches for this campaign
    await supabaseClient
      .from('matches')
      .delete()
      .eq('campaign_id', campaignId);

    // Insert new matches
    if (matches.length > 0) {
      const { error: insertError } = await supabaseClient
        .from('matches')
        .insert(matches);

      if (insertError) throw insertError;
    }

    console.log(`Generated ${matches.length} matches for campaign ${campaign.name}`);

    return new Response(
      JSON.stringify({ 
        success: true, 
        matchCount: matches.length,
        message: `Generated ${matches.length} AI-powered matches` 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error in generate-matches function:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return new Response(
      JSON.stringify({ error: errorMessage }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
